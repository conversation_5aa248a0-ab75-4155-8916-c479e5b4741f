import React, { useState } from 'react';
import PaymentRequestFormController from '../components/PaymentRequestFormController';
import { PaymentRequestData } from '../components/PaymentRequestForm';

const PaymentRequestFormScreen: React.FC = () => {
  const [formData, setFormData] = useState<PaymentRequestData>({
    date: '24 Jul 2025',
    amount: '857,496.52',
    fromAccount: {
      name: 'E-POS Service Company Limited',
      type: 'Current Account',
      bank: 'SCB',
      branch: 'Silom Branch',
      accountNo: '224-0-33223-5',
    },
    transactions: {
      merchantGross: '855,987.00',
      merchantNet: '9,880.85',
      discountEarned: '7,889.10',
      vat: '582.23',
    },
    description: 'With Holding Tax 3% Amount Thb 304.52',
    dateRange: {
      from: '24 Jul 2025',
      to: '26 Jul 2025',
    },
    mediaClearing: {
      totalTransactions: '42',
      sumTotal: '7,889.10',
    },
    summary: {
      transferDate: 'Direct Credit SCB',
      totalAmount: '857,496.52',
      lessDiscount: '848,706.84',
      vatAmount: '576.27',
      netAmount: '855,987.00',
    },
  });

  const handleDataChange = (newData: PaymentRequestData) => {
    setFormData(newData);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Request Form</h1>
        <p className="text-gray-600">
          Generate and manage payment request forms with customizable data and PDF export capabilities.
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-6">
        <PaymentRequestFormController
          data={formData}
          onDataChange={handleDataChange}
          showForm={true}
        />
      </div>

      <div className="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-medium mb-2">Features</h3>
        <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
          <li>Dynamic form data editing with real-time preview</li>
          <li>PDF generation using react-pdf/renderer</li>
          <li>Professional payment request form layout</li>
          <li>Export to PDF with custom filename</li>
          <li>Preview functionality before download</li>
          <li>Company account and transaction details management</li>
          <li>Signature sections for approvals</li>
        </ul>
      </div>

      <div className="mt-6 bg-blue-50 rounded-lg p-4">
        <h3 className="text-lg font-medium mb-2 text-blue-800">Usage Instructions</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm text-blue-700">
          <li>Fill in the form fields with your payment request details</li>
          <li>Click "Preview" to view the generated PDF in a new window</li>
          <li>Click "Download PDF" to save the form to your computer</li>
          <li>The form includes all necessary sections for payment approval workflow</li>
          <li>All fields are customizable and will reflect in the generated PDF</li>
        </ol>
      </div>
    </div>
  );
};

export default PaymentRequestFormScreen;
